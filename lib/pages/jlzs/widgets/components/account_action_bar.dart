import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 账号操作按钮栏组件
/// 
/// 从 AccountManageDialog 的操作按钮栏部分提取
/// 保持原有的样式和逻辑完全不变
class AccountActionBar extends StatelessWidget {
  final VoidCallback onAddAccount;
  final VoidCallback onRefreshAll;
  final VoidCallback onCheckTokens;
  final VoidCallback onToggleBatchMode;
  final VoidCallback onBatchDelete;
  final VoidCallback onBatchRefreshToken;
  final VoidCallback onCancelBatch;
  final bool isSelectMode;
  final int selectedCount;
  final int totalCount;

  const AccountActionBar({
    Key? key,
    required this.onAddAccount,
    required this.onRefreshAll,
    required this.onCheckTokens,
    required this.onToggleBatchMode,
    required this.onBatchDelete,
    required this.onBatchRefreshToken,
    required this.onCancelBatch,
    required this.isSelectMode,
    required this.selectedCount,
    required this.totalCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 操作按钮栏
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: onAddAccount,
              icon: const Icon(Icons.add, size: 18),
              label: const Text('添加账号'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade600,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton.icon(
              onPressed: onRefreshAll,
              icon: const Icon(Icons.refresh, size: 18),
              label: const Text('刷新状态'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton.icon(
              onPressed: onCheckTokens,
              icon: const Icon(Icons.security, size: 18),
              label: const Text('检查Token'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple.shade600,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 12),

            if (!isSelectMode) ...[
              ElevatedButton.icon(
                onPressed: onToggleBatchMode,
                icon: const Icon(Icons.checklist, size: 18),
                label: const Text('批量操作'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.shade600,
                  foregroundColor: Colors.white,
                ),
              ),
            ] else ...[
              ElevatedButton.icon(
                onPressed: onBatchDelete,
                icon: const Icon(Icons.delete, size: 18),
                label: Text('删除($selectedCount)'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade600,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                onPressed: onBatchRefreshToken,
                icon: const Icon(Icons.refresh, size: 18),
                label: Text('刷新Token($selectedCount)'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.shade600,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              TextButton(
                onPressed: onCancelBatch,
                child: const Text('取消'),
              ),
            ],
            const Spacer(),
            Text(
              '共 $totalCount 个账号',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
