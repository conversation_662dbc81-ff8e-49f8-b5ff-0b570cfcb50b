import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../model/jlzs_account_new_model.dart';
import 'account_card_widget.dart';

/// 账号列表组件
/// 
/// 从 AccountManageDialog 的账号列表部分提取
/// 保持原有的样式和逻辑完全不变
class AccountListWidget extends StatelessWidget {
  final RxList<JlzsAccountModel> accounts;
  final bool isSelectMode;
  final Set<String> selectedAccounts;
  final Function(String) onAccountSelect;
  final Function(JlzsAccountModel, AccountAction) onAccountAction;

  const AccountListWidget({
    Key? key,
    required this.accounts,
    required this.isSelectMode,
    required this.selectedAccounts,
    required this.onAccountSelect,
    required this.onAccountAction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Obx(() {
        if (accounts.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.account_circle_outlined,
                     size: 64, color: Colors.grey.shade400),
                const SizedBox(height: 16),
                Text(
                  '暂无账号',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '点击"添加账号"按钮添加第一个账号',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: accounts.length,
          itemBuilder: (context, index) {
            final account = accounts[index];
            return AccountCardWidget(
              account: account,
              isSelectMode: isSelectMode,
              isSelected: selectedAccounts.contains(account.id),
              onSelect: () => onAccountSelect(account.id),
              onAction: (action) => onAccountAction(account, action),
            );
          },
        );
      }),
    );
  }
}
